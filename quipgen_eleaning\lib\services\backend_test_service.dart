import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/course.dart';
import '../models/category.dart';
import '../models/enrollment.dart';
import 'api_service.dart';
import 'connectivity_service.dart';
import 'error_handler.dart';

class BackendTestService {
  static final BackendTestService _instance = BackendTestService._internal();
  factory BackendTestService() => _instance;
  BackendTestService._internal();

  final ApiService _apiService = ApiService();
  final ConnectivityService _connectivityService = ConnectivityService();

  // Test results
  final Map<String, TestResult> _testResults = {};

  Map<String, TestResult> get testResults => Map.unmodifiable(_testResults);

  Future<void> runAllTests() async {
    if (!kDebugMode) {
      print('Backend tests can only be run in debug mode');
      return;
    }

    print('🧪 Starting Backend Integration Tests...');
    _testResults.clear();

    // Check connectivity first
    await _testConnectivity();
    
    // Test API endpoints
    await _testHealthEndpoint();
    await _testCourseEndpoints();
    await _testCategoryEndpoints();
    
    // Test authentication endpoints (if user is logged in)
    if (FirebaseAuth.instance.currentUser != null) {
      await _testAuthenticationEndpoints();
      await _testUserEndpoints();
      await _testPaymentEndpoints();
    } else {
      print('⚠️ Skipping authenticated endpoints (user not logged in)');
    }

    _printTestSummary();
  }

  Future<void> _testConnectivity() async {
    final testName = 'Connectivity Check';
    try {
      final isConnected = await _connectivityService.hasInternetConnection();
      _recordTestResult(testName, isConnected, 
          isConnected ? 'Internet connection available' : 'No internet connection');
    } catch (e) {
      _recordTestResult(testName, false, 'Connectivity test failed: $e');
    }
  }

  Future<void> _testHealthEndpoint() async {
    final testName = 'Health Endpoint';
    try {
      // Note: Health endpoint might not be implemented in current API service
      // This is a placeholder for when it's added
      _recordTestResult(testName, true, 'Health endpoint not implemented yet');
    } catch (e) {
      _recordTestResult(testName, false, 'Health endpoint test failed: $e');
    }
  }

  Future<void> _testCourseEndpoints() async {
    await _testGetCourses();
    await _testGetFeaturedCourses();
    await _testGetPopularCourses();
    await _testGetCourseDetails();
  }

  Future<void> _testGetCourses() async {
    final testName = 'Get Courses';
    try {
      final courses = await _apiService.getCourses(limit: 5);
      final success = courses.isNotEmpty;
      _recordTestResult(testName, success, 
          success ? 'Found ${courses.length} courses' : 'No courses returned');
    } catch (e) {
      _recordTestResult(testName, false, 'Get courses failed: $e');
    }
  }

  Future<void> _testGetFeaturedCourses() async {
    final testName = 'Get Featured Courses';
    try {
      final courses = await _apiService.getFeaturedCourses(limit: 3);
      _recordTestResult(testName, true, 'Found ${courses.length} featured courses');
    } catch (e) {
      _recordTestResult(testName, false, 'Get featured courses failed: $e');
    }
  }

  Future<void> _testGetPopularCourses() async {
    final testName = 'Get Popular Courses';
    try {
      final courses = await _apiService.getPopularCourses(limit: 3);
      _recordTestResult(testName, true, 'Found ${courses.length} popular courses');
    } catch (e) {
      _recordTestResult(testName, false, 'Get popular courses failed: $e');
    }
  }

  Future<void> _testGetCourseDetails() async {
    final testName = 'Get Course Details';
    try {
      // First get a course to test details
      final courses = await _apiService.getCourses(limit: 1);
      if (courses.isNotEmpty) {
        final courseId = int.tryParse(courses.first.id);
        if (courseId != null) {
          final course = await _apiService.getCourseById(courseId);
          _recordTestResult(testName, true, 'Course details retrieved: ${course.title}');
        } else {
          _recordTestResult(testName, false, 'Invalid course ID format');
        }
      } else {
        _recordTestResult(testName, false, 'No courses available to test details');
      }
    } catch (e) {
      _recordTestResult(testName, false, 'Get course details failed: $e');
    }
  }

  Future<void> _testCategoryEndpoints() async {
    final testName = 'Get Categories';
    try {
      final categories = await _apiService.getCategories();
      final success = categories.isNotEmpty;
      _recordTestResult(testName, success, 
          success ? 'Found ${categories.length} categories' : 'No categories returned');
    } catch (e) {
      _recordTestResult(testName, false, 'Get categories failed: $e');
    }
  }

  Future<void> _testAuthenticationEndpoints() async {
    await _testGetUserProfile();
    await _testVerifyToken();
  }

  Future<void> _testGetUserProfile() async {
    final testName = 'Get User Profile';
    try {
      final profile = await _apiService.getUserProfile();
      final success = profile.isNotEmpty;
      _recordTestResult(testName, success, 
          success ? 'Profile retrieved for user: ${profile['email']}' : 'Empty profile returned');
    } catch (e) {
      _recordTestResult(testName, false, 'Get user profile failed: $e');
    }
  }

  Future<void> _testVerifyToken() async {
    final testName = 'Verify Token';
    try {
      final result = await _apiService.verifyToken();
      _recordTestResult(testName, true, 'Token verification successful');
    } catch (e) {
      _recordTestResult(testName, false, 'Token verification failed: $e');
    }
  }

  Future<void> _testUserEndpoints() async {
    await _testGetUserEnrollments();
  }

  Future<void> _testGetUserEnrollments() async {
    final testName = 'Get User Enrollments';
    try {
      final enrollments = await _apiService.getUserEnrollments(limit: 5);
      _recordTestResult(testName, true, 'Found ${enrollments.length} enrollments');
    } catch (e) {
      _recordTestResult(testName, false, 'Get user enrollments failed: $e');
    }
  }

  Future<void> _testPaymentEndpoints() async {
    final testName = 'Payment Integration Test';
    try {
      // Test creating a payment order (this creates a real order, use carefully)
      if (kDebugMode) {
        final orderData = await _apiService.createPaymentOrder(1);
        final success = orderData['orderId'] != null;
        _recordTestResult(testName, success, 
            success ? 'Payment order created: ${orderData['orderId']}' : 'No order ID returned');
      } else {
        _recordTestResult(testName, true, 'Payment test skipped in production');
      }
    } catch (e) {
      _recordTestResult(testName, false, 'Payment test failed: $e');
    }
  }

  void _recordTestResult(String testName, bool success, String message) {
    _testResults[testName] = TestResult(
      name: testName,
      success: success,
      message: message,
      timestamp: DateTime.now(),
    );
    
    final icon = success ? '✅' : '❌';
    print('$icon $testName: $message');
  }

  void _printTestSummary() {
    final totalTests = _testResults.length;
    final passedTests = _testResults.values.where((result) => result.success).length;
    final failedTests = totalTests - passedTests;

    print('\n📊 Test Summary:');
    print('Total Tests: $totalTests');
    print('Passed: $passedTests');
    print('Failed: $failedTests');
    print('Success Rate: ${((passedTests / totalTests) * 100).toStringAsFixed(1)}%');

    if (failedTests > 0) {
      print('\n❌ Failed Tests:');
      _testResults.values
          .where((result) => !result.success)
          .forEach((result) {
        print('  • ${result.name}: ${result.message}');
      });
    }

    print('\n🏁 Backend Integration Tests Complete!');
  }

  // Quick test for specific functionality
  Future<bool> testBasicConnectivity() async {
    try {
      await _apiService.getCategories();
      return true;
    } catch (e) {
      print('Basic connectivity test failed: $e');
      return false;
    }
  }

  // Test specific course operations
  Future<bool> testCourseOperations(int courseId) async {
    try {
      final course = await _apiService.getCourseById(courseId);
      final lessons = await _apiService.getCourseLessons(courseId);
      print('Course test successful: ${course.title} with ${lessons.length} lessons');
      return true;
    } catch (e) {
      print('Course operations test failed: $e');
      return false;
    }
  }
}

class TestResult {
  final String name;
  final bool success;
  final String message;
  final DateTime timestamp;

  TestResult({
    required this.name,
    required this.success,
    required this.message,
    required this.timestamp,
  });

  @override
  String toString() => '$name: ${success ? 'PASS' : 'FAIL'} - $message';
}
