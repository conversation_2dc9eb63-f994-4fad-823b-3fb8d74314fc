import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

class FirebaseTestHelper {
  static Future<Map<String, dynamic>> testFirebaseConnection() async {
    final results = <String, dynamic>{};
    
    try {
      // Test Firebase Core initialization
      final app = Firebase.app();
      results['firebase_core'] = {
        'status': 'success',
        'app_name': app.name,
        'project_id': app.options.projectId,
      };
    } catch (e) {
      results['firebase_core'] = {
        'status': 'error',
        'error': e.toString(),
      };
    }
    
    try {
      // Test Firebase Auth initialization
      final auth = FirebaseAuth.instance;
      results['firebase_auth'] = {
        'status': 'success',
        'current_user': auth.currentUser?.email ?? 'No user signed in',
        'app_name': auth.app.name,
      };
    } catch (e) {
      results['firebase_auth'] = {
        'status': 'error',
        'error': e.toString(),
      };
    }
    
    return results;
  }
  
  static void showFirebaseStatus(BuildContext context) async {
    final results = await testFirebaseConnection();
    
    if (!context.mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Firebase Status'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatusSection('Firebase Core', results['firebase_core']),
              const SizedBox(height: 16),
              _buildStatusSection('Firebase Auth', results['firebase_auth']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
  
  static Widget _buildStatusSection(String title, Map<String, dynamic> data) {
    final isSuccess = data['status'] == 'success';
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              isSuccess ? Icons.check_circle : Icons.error,
              color: isSuccess ? Colors.green : Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (isSuccess) ...[
          if (data.containsKey('app_name'))
            Text('App: ${data['app_name']}'),
          if (data.containsKey('project_id'))
            Text('Project: ${data['project_id']}'),
          if (data.containsKey('current_user'))
            Text('User: ${data['current_user']}'),
        ] else ...[
          Text(
            'Error: ${data['error']}',
            style: const TextStyle(color: Colors.red, fontSize: 12),
          ),
        ],
      ],
    );
  }
}
