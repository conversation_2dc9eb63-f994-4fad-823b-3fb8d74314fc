import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

enum ConnectionStatus {
  online,
  offline,
  unknown,
}

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  ConnectionStatus _status = ConnectionStatus.unknown;
  final StreamController<ConnectionStatus> _statusController = 
      StreamController<ConnectionStatus>.broadcast();

  ConnectionStatus get status => _status;
  Stream<ConnectionStatus> get statusStream => _statusController.stream;

  void initialize() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
    );
    
    // Check initial connectivity
    _checkConnectivity();
  }

  Future<void> _checkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      _updateConnectionStatus(connectivityResults);
    } catch (e) {
      print('Error checking connectivity: $e');
      _updateStatus(ConnectionStatus.unknown);
    }
  }

  void _updateConnectionStatus(List<ConnectivityResult> results) {
    if (results.contains(ConnectivityResult.none)) {
      _updateStatus(ConnectionStatus.offline);
    } else {
      // Even if we have connectivity, verify with actual network request
      _verifyInternetConnection();
    }
  }

  Future<void> _verifyInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        _updateStatus(ConnectionStatus.online);
      } else {
        _updateStatus(ConnectionStatus.offline);
      }
    } catch (e) {
      _updateStatus(ConnectionStatus.offline);
    }
  }

  void _updateStatus(ConnectionStatus status) {
    if (_status != status) {
      _status = status;
      _statusController.add(status);
      
      if (kDebugMode) {
        print('Connection status changed: ${status.name}');
      }
    }
  }

  Future<bool> get isConnected async {
    if (_status == ConnectionStatus.unknown) {
      await _checkConnectivity();
    }
    return _status == ConnectionStatus.online;
  }

  Future<bool> hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 3));
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _statusController.close();
  }
}

// Mixin for widgets that need connectivity awareness
mixin ConnectivityAware {
  StreamSubscription<ConnectionStatus>? _connectivitySubscription;
  
  void startListeningToConnectivity(Function(ConnectionStatus) onStatusChanged) {
    _connectivitySubscription = ConnectivityService()
        .statusStream
        .listen(onStatusChanged);
  }
  
  void stopListeningToConnectivity() {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
  }
}

// Helper class for network-aware operations
class NetworkAwareOperation<T> {
  final Future<T> Function() onlineOperation;
  final Future<T> Function()? offlineOperation;
  final T? fallbackData;
  final String operationName;

  NetworkAwareOperation({
    required this.onlineOperation,
    this.offlineOperation,
    this.fallbackData,
    required this.operationName,
  });

  Future<T> execute() async {
    final connectivityService = ConnectivityService();
    
    try {
      // Try online operation first if connected
      if (await connectivityService.isConnected) {
        if (kDebugMode) {
          print('Executing $operationName online');
        }
        return await onlineOperation();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Online operation failed for $operationName: $e');
      }
    }

    // Try offline operation if available
    if (offlineOperation != null) {
      try {
        if (kDebugMode) {
          print('Executing $operationName offline');
        }
        return await offlineOperation!();
      } catch (e) {
        if (kDebugMode) {
          print('Offline operation failed for $operationName: $e');
        }
      }
    }

    // Use fallback data if available
    if (fallbackData != null) {
      if (kDebugMode) {
        print('Using fallback data for $operationName');
      }
      return fallbackData!;
    }

    // If all else fails, throw an error
    throw Exception('No connectivity and no fallback available for $operationName');
  }
}

// Extension for easy network-aware API calls
extension NetworkAwareApiCall<T> on Future<T> Function() {
  NetworkAwareOperation<T> withFallback({
    Future<T> Function()? offlineOperation,
    T? fallbackData,
    required String operationName,
  }) {
    return NetworkAwareOperation<T>(
      // ignore: unnecessary_cast
      onlineOperation: this as Future<T> Function(),
      offlineOperation: offlineOperation,
      fallbackData: fallbackData,
      operationName: operationName,
    );
  }
}
