# Flutter/Dart/Pub related
.dart_tool/
.packages
.pub/
.pub-cache/
build/
.flutter-plugins
.flutter-plugins-dependencies
.flutter-versions

# IDEs and editors
.idea/
*.iml
*.ipr
*.iws
.vscode/
*.sublime-workspace
*.sublime-project

# macOS
.DS_Store

# Miscellaneous
*.class
*.log
*.pyc
*.swp
*.swo
*.bak
*.tmp
*.orig
*.rej

# Android
android/.gradle/
android/app/debug/
android/app/profile/
android/app/release/

# iOS
ios/Pods/
ios/Flutter/Flutter.framework/
ios/Flutter/Flutter.podspec
ios/Flutter/Generated.xcconfig
ios/Flutter/app.flx
ios/Flutter/app.zip
ios/Flutter/flutter_assets/
ios/ServiceDefinitions.json
ios/Runner/GeneratedPluginRegistrant.*

# Symbolication and obfuscation
app.*.symbols
app.*.map.json

# Test output
test-results/
coverage/

# Database/Secrets
*.sqlite
*.db
*.p12
*.key
*.pem
*.crt
firebase-service-account.json

# System
Thumbs.db
