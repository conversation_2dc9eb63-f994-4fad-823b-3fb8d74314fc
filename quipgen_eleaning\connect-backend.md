# Flutter-Backend Integration Guide

This comprehensive guide covers the complete integration between the QuipGen E-Learning Flutter app and Node.js backend.

## Table of Contents
1. [Backend API Overview](#backend-api-overview)
2. [Environment Setup](#environment-setup)
3. [Authentication System](#authentication-system)
4. [API Endpoints Reference](#api-endpoints-reference)
5. [Flutter Integration Implementation](#flutter-integration-implementation)
6. [Course Purchase Workflow](#course-purchase-workflow)
7. [Core User Flows](#core-user-flows)
8. [Testing and Debugging](#testing-and-debugging)
9. [Troubleshooting](#troubleshooting)

## Backend API Overview

### Base Configuration
- **Base URL**: `http://localhost:3001/api`
- **Content-Type**: `application/json`
- **Authentication**: Firebase JWT tokens via `Authorization: Bearer <token>`
- **Error Format**: Standardized JSON error responses

### Available Endpoints Summary
| Category | Endpoint                             | Method | Auth Required |
| -------- | ------------------------------------ | ------ | ------------- |
| Health   | `/health`                            | GET    | No            |
| Auth     | `/auth/profile`                      | GET    | Yes           |
| Auth     | `/auth/profile`                      | PUT    | Yes           |
| Auth     | `/auth/verify`                       | GET    | Yes           |
| Courses  | `/courses`                           | GET    | Optional      |
| Courses  | `/courses/:id`                       | GET    | Optional      |
| Courses  | `/courses/categories`                | GET    | No            |
| Courses  | `/courses/featured`                  | GET    | Optional      |
| Courses  | `/courses/popular`                   | GET    | Optional      |
| User     | `/user/enrollments`                  | GET    | Yes           |
| User     | `/user/progress/:courseId`           | GET    | Yes           |
| User     | `/user/progress/:courseId/:lessonId` | PUT    | Yes           |
| Lessons  | `/lessons/course/:courseId`          | GET    | Optional      |
| Lessons  | `/lessons/:id`                       | GET    | Yes           |
| Lessons  | `/lessons/:id/access`                | POST   | Yes           |
| Payment  | `/payment/create-order`              | POST   | Yes           |
| Payment  | `/payment/verify`                    | POST   | Yes           |
| Payment  | `/payment/history`                   | GET    | Yes           |

## Environment Setup

### Backend Setup

1. **Install Dependencies**
```bash
cd backend
npm install
```

2. **Environment Configuration** (`.env`)
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=quipgen_elearning
DB_USER=postgres
DB_PASSWORD=your_password

# Firebase Configuration
FIREBASE_ADMIN_SDK_PATH=./src/config/firebase-admin-sdk.json
FIREBASE_PROJECT_ID=your_firebase_project_id

# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Server Configuration
PORT=3001
NODE_ENV=development
```

3. **Database Setup**
```bash
# Run migrations
node src/database/migrate.js up

# Seed sample data
npm run seed
```

4. **Start Server**
```bash
npm run dev
```

### Flutter Setup

1. **Add Dependencies** (`pubspec.yaml`)
```yaml
dependencies:
  http: ^1.1.0
  shared_preferences: ^2.2.2
  firebase_auth: ^4.17.0
```

2. **Run Flutter**
```bash
flutter pub get
flutter run
```

## Authentication System

### Firebase Token Flow

1. **User Login in Flutter**
```dart
// Firebase Authentication
final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
  email: email,
  password: password,
);

// Get ID Token
final idToken = await credential.user?.getIdToken();
```

2. **Token Usage in API Calls**
```dart
final headers = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer $idToken',
};
```

3. **Backend Token Verification**
The backend automatically verifies Firebase tokens and creates/updates user records.

### Test Mode (Without Firebase)
When Firebase credentials are not available, the backend runs in test mode with a mock user:
```json
{
  "id": 1,
  "firebaseUid": "test-user-uid",
  "email": "<EMAIL>",
  "name": "Test User"
}
```

## API Endpoints Reference

### Health Check
```http
GET /health
```
**Response:**
```json
{
  "status": "OK",
  "timestamp": "2025-08-04T01:00:00.000Z",
  "environment": "development"
}
```

### Authentication Endpoints

#### Get User Profile
```http
GET /api/auth/profile
Authorization: Bearer <firebase_token>
```
**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "firebaseUid": "firebase_uid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "phone": "+**********",
    "profileImageUrl": "https://example.com/image.jpg",
    "emailVerified": true,
    "isAdmin": false,
    "createdAt": "2025-08-04T00:00:00.000Z",
    "updatedAt": "2025-08-04T00:00:00.000Z"
  }
}
```

#### Update User Profile
```http
PUT /api/auth/profile
Authorization: Bearer <firebase_token>
Content-Type: application/json

{
  "name": "Updated Name",
  "phone": "+**********"
}
```

### Course Endpoints

#### Get All Courses
```http
GET /api/courses?page=1&limit=10&category=Development&level=Beginner&search=flutter
```
**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `category`: Filter by category name
- `level`: Filter by difficulty level
- `search`: Search in title/description
- `featured`: true/false for featured courses
- `popular`: true/false for popular courses

**Response:**
```json
{
  "success": true,
  "data": {
    "courses": [
      {
        "id": 1,
        "title": "Flutter App Development",
        "description": "Learn Flutter from scratch",
        "imageUrl": "https://example.com/image.jpg",
        "instructor": "John Doe",
        "price": 29.99,
        "rating": 4.8,
        "totalLessons": 30,
        "duration": 600,
        "level": "Intermediate",
        "category": "Development",
        "categoryColor": "#4ECDC4",
        "isFeatured": true,
        "isPopular": false,
        "createdAt": "2025-08-04T00:00:00.000Z",
        "updatedAt": "2025-08-04T00:00:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalCount": 50,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### Get Course Details
```http
GET /api/courses/1
Authorization: Bearer <firebase_token> (optional)
```
**Response includes additional fields:**
```json
{
  "success": true,
  "data": {
    // ... course fields ...
    "tags": ["Flutter", "Mobile", "Dart"],
    "isEnrolled": true,
    "stats": {
      "totalEnrollments": 150,
      "averageRating": 4.7,
      "totalReviews": 45
    }
  }
}
```

#### Get Categories
```http
GET /api/courses/categories
```
**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Development",
      "description": "Programming, Web & Mobile Development",
      "iconName": "code",
      "color": "#4ECDC4",
      "isPopular": true,
      "courseCount": 25,
      "createdAt": "2025-08-04T00:00:00.000Z"
    }
  ]
}
```

#### Get Featured Courses
```http
GET /api/courses/featured?limit=6
```

#### Get Popular Courses
```http
GET /api/courses/popular?limit=6
```

### User Management Endpoints

#### Get User Enrollments
```http
GET /api/user/enrollments?page=1&limit=10
Authorization: Bearer <firebase_token>
```
**Response:**
```json
{
  "success": true,
  "data": {
    "enrollments": [
      {
        "id": 1,
        "courseId": 1,
        "paymentStatus": "completed",
        "enrolledAt": "2025-08-04T00:00:00.000Z",
        "paymentCompletedAt": "2025-08-04T00:00:00.000Z",
        "course": {
          // ... course details ...
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalCount": 15,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### Get Course Progress
```http
GET /api/user/progress/1
Authorization: Bearer <firebase_token>
```
**Response:**
```json
{
  "success": true,
  "data": {
    "courseId": 1,
    "courseTitle": "Flutter App Development",
    "totalLessons": 30,
    "completedLessons": 15,
    "overallProgress": 50.0,
    "totalTimeSpent": 450,
    "lessons": [
      {
        "lessonId": 1,
        "lessonTitle": "Introduction to Flutter",
        "orderIndex": 1,
        "isCompleted": true,
        "progressPercentage": 100.0,
        "timeSpentMinutes": 25,
        "lastAccessedAt": "2025-08-04T00:00:00.000Z",
        "completedAt": "2025-08-04T00:00:00.000Z"
      }
    ]
  }
}
```

#### Update Lesson Progress
```http
PUT /api/user/progress/1/1
Authorization: Bearer <firebase_token>
Content-Type: application/json

{
  "progressPercentage": 100.0,
  "timeSpentMinutes": 25,
  "isCompleted": true
}
```

### Lesson Endpoints

#### Get Course Lessons
```http
GET /api/lessons/course/1
Authorization: Bearer <firebase_token> (optional)
```
**Response:**
```json
{
  "success": true,
  "data": {
    "courseId": 1,
    "courseTitle": "Flutter App Development",
    "isEnrolled": true,
    "lessons": [
      {
        "id": 1,
        "title": "Introduction to Flutter",
        "description": "Learn the basics of Flutter",
        "videoUrl": "https://example.com/video1.mp4",
        "duration": 25,
        "order": 1,
        "isPreview": true,
        "isCompleted": false,
        "progressPercentage": 0,
        "timeSpentMinutes": 0,
        "lastAccessedAt": null,
        "isLocked": false,
        "createdAt": "2025-08-04T00:00:00.000Z"
      }
    ]
  }
}
```

#### Get Lesson Details
```http
GET /api/lessons/1
Authorization: Bearer <firebase_token>
```

#### Mark Lesson as Accessed
```http
POST /api/lessons/1/access
Authorization: Bearer <firebase_token>
```

### Payment Endpoints

#### Create Payment Order
```http
POST /api/payment/create-order
Authorization: Bearer <firebase_token>
Content-Type: application/json

{
  "courseId": 1,
  "currency": "INR"
}
```
**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "order_razorpay_id",
    "amount": 2999,
    "currency": "INR",
    "courseId": 1,
    "courseTitle": "Flutter App Development",
    "coursePrice": 29.99
  }
}
```

#### Verify Payment
```http
POST /api/payment/verify
Authorization: Bearer <firebase_token>
Content-Type: application/json

{
  "razorpay_order_id": "order_razorpay_id",
  "razorpay_payment_id": "pay_razorpay_id",
  "razorpay_signature": "signature_hash",
  "course_id": 1
}
```
**Response:**
```json
{
  "success": true,
  "data": {
    "enrollmentId": 1,
    "courseId": 1,
    "paymentStatus": "completed",
    "message": "Payment verified and enrollment completed successfully"
  }
}
```

#### Get Payment History
```http
GET /api/payment/history?page=1&limit=10
Authorization: Bearer <firebase_token>
```

## Flutter Integration Implementation

### 1. API Configuration Setup

Create `lib/services/api_config.dart`:
```dart
class ApiConfig {
  static const String baseUrl = 'http://localhost:3001/api';
  
  // For Android emulator, use: http://********:3001/api
  // For iOS simulator, use: http://localhost:3001/api
  
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  static String get coursesUrl => '$baseUrl/courses';
  static String get authUrl => '$baseUrl/auth';
  static String get userUrl => '$baseUrl/user';
  static String get lessonsUrl => '$baseUrl/lessons';
  static String get paymentUrl => '$baseUrl/payment';
}
```

### 2. HTTP Client Implementation

Create `lib/services/http_client.dart`:
```dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:firebase_auth/firebase_auth.dart';
import 'api_config.dart';

class HttpClient {
  static final HttpClient _instance = HttpClient._internal();
  factory HttpClient() => _instance;
  HttpClient._internal();

  Future<String?> _getAuthToken() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      return await user?.getIdToken();
    } catch (e) {
      print('Error getting auth token: $e');
      return null;
    }
  }

  Future<Map<String, String>> _buildHeaders({
    Map<String, String>? additionalHeaders
  }) async {
    final headers = Map<String, String>.from(ApiConfig.defaultHeaders);
    
    final token = await _getAuthToken();
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }
    
    return headers;
  }

  Future<dynamic> get(String url, {Map<String, String>? queryParams}) async {
    try {
      final uri = Uri.parse(url);
      final finalUri = queryParams != null 
          ? uri.replace(queryParameters: queryParams)
          : uri;
      
      final headers = await _buildHeaders();
      final response = await http.get(finalUri, headers: headers);
      
      return _handleResponse(response);
    } catch (e) {
      throw Exception('GET request failed: $e');
    }
  }

  Future<dynamic> post(String url, {dynamic body}) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: body != null ? json.encode(body) : null,
      );
      
      return _handleResponse(response);
    } catch (e) {
      throw Exception('POST request failed: $e');
    }
  }

  Future<dynamic> put(String url, {dynamic body}) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.put(
        Uri.parse(url),
        headers: headers,
        body: body != null ? json.encode(body) : null,
      );
      
      return _handleResponse(response);
    } catch (e) {
      throw Exception('PUT request failed: $e');
    }
  }

  dynamic _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    
    try {
      final data = json.decode(response.body);
      
      if (statusCode >= 200 && statusCode < 300) {
        return data;
      } else {
        final message = data['message'] ?? 'Unknown error occurred';
        throw Exception('HTTP $statusCode: $message');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Failed to parse response: $e');
    }
  }
}
```

### 3. API Service Implementation

Create `lib/services/api_service.dart`:
```dart
import '../models/course.dart';
import '../models/category.dart';
import '../models/lesson.dart';
import '../models/enrollment.dart';
import 'http_client.dart';
import 'api_config.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final HttpClient _httpClient = HttpClient();

  // Course endpoints
  Future<List<Course>> getCourses({
    int page = 1,
    int limit = 10,
    String? category,
    String? level,
    String? search,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (category != null) queryParams['category'] = category;
    if (level != null) queryParams['level'] = level;
    if (search != null) queryParams['search'] = search;

    final response = await _httpClient.get(
      ApiConfig.coursesUrl, 
      queryParams: queryParams
    );
    
    final coursesData = response['data']['courses'] as List;
    return coursesData.map((json) => Course.fromJson(json)).toList();
  }

  Future<Course> getCourseById(int courseId) async {
    final response = await _httpClient.get('${ApiConfig.coursesUrl}/$courseId');
    return Course.fromJson(response['data']);
  }

  Future<List<Course>> getFeaturedCourses({int limit = 6}) async {
    final queryParams = {'limit': limit.toString()};
    final response = await _httpClient.get(
      '${ApiConfig.coursesUrl}/featured',
      queryParams: queryParams
    );
    
    final coursesData = response['data'] as List;
    return coursesData.map((json) => Course.fromJson(json)).toList();
  }

  Future<List<Category>> getCategories() async {
    final response = await _httpClient.get('${ApiConfig.coursesUrl}/categories');
    final categoriesData = response['data'] as List;
    return categoriesData.map((json) => Category.fromJson(json)).toList();
  }

  // User endpoints
  Future<List<Enrollment>> getUserEnrollments({int page = 1, int limit = 10}) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    
    final response = await _httpClient.get(
      '${ApiConfig.userUrl}/enrollments',
      queryParams: queryParams
    );
    
    final enrollmentsData = response['data']['enrollments'] as List;
    return enrollmentsData.map((json) => Enrollment.fromJson(json)).toList();
  }

  Future<Map<String, dynamic>> getUserProgress(int courseId) async {
    final response = await _httpClient.get('${ApiConfig.userUrl}/progress/$courseId');
    return response['data'];
  }

  Future<void> updateLessonProgress(
    int courseId,
    int lessonId, {
    required double progressPercentage,
    int? timeSpentMinutes,
    bool? isCompleted,
  }) async {
    final body = {
      'progressPercentage': progressPercentage,
      if (timeSpentMinutes != null) 'timeSpentMinutes': timeSpentMinutes,
      if (isCompleted != null) 'isCompleted': isCompleted,
    };

    await _httpClient.put(
      '${ApiConfig.userUrl}/progress/$courseId/$lessonId',
      body: body,
    );
  }

  // Lesson endpoints
  Future<List<Lesson>> getCourseLessons(int courseId) async {
    final response = await _httpClient.get('${ApiConfig.lessonsUrl}/course/$courseId');
    final lessonsData = response['data']['lessons'] as List;
    return lessonsData.map((json) => Lesson.fromJson(json)).toList();
  }

  // Payment endpoints
  Future<Map<String, dynamic>> createPaymentOrder(int courseId) async {
    final body = {'courseId': courseId, 'currency': 'INR'};
    final response = await _httpClient.post('${ApiConfig.paymentUrl}/create-order', body: body);
    return response['data'];
  }

  Future<Map<String, dynamic>> verifyPayment({
    required String razorpayOrderId,
    required String razorpayPaymentId,
    required String razorpaySignature,
    required int courseId,
  }) async {
    final body = {
      'razorpay_order_id': razorpayOrderId,
      'razorpay_payment_id': razorpayPaymentId,
      'razorpay_signature': razorpaySignature,
      'course_id': courseId,
    };

    final response = await _httpClient.post('${ApiConfig.paymentUrl}/verify', body: body);
    return response['data'];
  }
}
```

### 4. Update Data Service

Update `lib/services/data_service.dart` to use the API service:
```dart
import '../models/course.dart';
import '../models/category.dart';
import 'api_service.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  final ApiService _apiService = ApiService();

  Future<List<Course>> getCourses() async {
    try {
      return await _apiService.getCourses();
    } catch (e) {
      print('Error fetching courses: $e');
      // Fallback to mock data if needed
      return [];
    }
  }

  Future<List<Course>> getFeaturedCourses() async {
    try {
      return await _apiService.getFeaturedCourses();
    } catch (e) {
      print('Error fetching featured courses: $e');
      return [];
    }
  }

  Future<List<Category>> getCategories() async {
    try {
      return await _apiService.getCategories();
    } catch (e) {
      print('Error fetching categories: $e');
      return [];
    }
  }

  Future<Course?> getCourseById(String courseId) async {
    try {
      final id = int.tryParse(courseId);
      if (id == null) throw Exception('Invalid course ID');
      return await _apiService.getCourseById(id);
    } catch (e) {
      print('Error fetching course details: $e');
      return null;
    }
  }
}
```

## Course Purchase Workflow

### 1. Razorpay Integration Setup

Add Razorpay dependency to `pubspec.yaml`:
```yaml
dependencies:
  razorpay_flutter: ^1.3.6
```

### 2. Payment Flow Implementation

```dart
import 'package:razorpay_flutter/razorpay_flutter.dart';

class PaymentService {
  late Razorpay _razorpay;
  
  void initializeRazorpay() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  Future<void> purchaseCourse(Course course) async {
    try {
      // Step 1: Create payment order
      final orderData = await ApiService().createPaymentOrder(course.id);
      
      // Step 2: Open Razorpay checkout
      var options = {
        'key': 'your_razorpay_key_id',
        'amount': orderData['amount'],
        'currency': orderData['currency'],
        'name': 'QuipGen E-Learning',
        'description': course.title,
        'order_id': orderData['orderId'],
        'prefill': {
          'contact': '9999999999',
          'email': '<EMAIL>'
        }
      };
      
      _razorpay.open(options);
    } catch (e) {
      print('Error creating payment order: $e');
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    try {
      // Step 3: Verify payment with backend
      await ApiService().verifyPayment(
        razorpayOrderId: response.orderId!,
        razorpayPaymentId: response.paymentId!,
        razorpaySignature: response.signature!,
        courseId: currentCourseId, // Store this when starting payment
      );
      
      // Step 4: Show success message and navigate to course
      print('Payment successful! Course enrolled.');
    } catch (e) {
      print('Payment verification failed: $e');
    }
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print('Payment failed: ${response.message}');
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print('External wallet selected: ${response.walletName}');
  }

  void dispose() {
    _razorpay.clear();
  }
}
```

## Core User Flows

### 1. Course Browsing Implementation

```dart
class CourseListScreen extends StatefulWidget {
  @override
  _CourseListScreenState createState() => _CourseListScreenState();
}

class _CourseListScreenState extends State<CourseListScreen> {
  final DataService _dataService = DataService();
  List<Course> courses = [];
  List<Category> categories = [];
  bool isLoading = true;
  String? selectedCategory;
  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => isLoading = true);
    
    try {
      final [coursesList, categoriesList] = await Future.wait([
        _dataService.getCourses(),
        _dataService.getCategories(),
      ]);
      
      setState(() {
        courses = coursesList as List<Course>;
        categories = categoriesList as List<Category>;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading data: $e');
      setState(() => isLoading = false);
    }
  }

  Future<void> _searchCourses() async {
    if (searchQuery.isEmpty && selectedCategory == null) {
      _loadData();
      return;
    }

    setState(() => isLoading = true);
    
    try {
      final searchResults = await ApiService().getCourses(
        search: searchQuery.isNotEmpty ? searchQuery : null,
        category: selectedCategory,
      );
      
      setState(() {
        courses = searchResults;
        isLoading = false;
      });
    } catch (e) {
      print('Error searching courses: $e');
      setState(() => isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Courses'),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(120),
          child: Column(
            children: [
              // Search bar
              Padding(
                padding: EdgeInsets.all(16),
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search courses...',
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    searchQuery = value;
                    _searchCourses();
                  },
                ),
              ),
              // Category filter
              Container(
                height: 50,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: categories.length + 1,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return _buildCategoryChip('All', null);
                    }
                    final category = categories[index - 1];
                    return _buildCategoryChip(category.name, category.name);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: courses.length,
              itemBuilder: (context, index) {
                final course = courses[index];
                return CourseCard(
                  course: course,
                  onTap: () => _navigateToCourseDetails(course),
                );
              },
            ),
    );
  }

  Widget _buildCategoryChip(String label, String? value) {
    final isSelected = selectedCategory == value;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            selectedCategory = selected ? value : null;
          });
          _searchCourses();
        },
      ),
    );
  }

  void _navigateToCourseDetails(Course course) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CourseDetailsScreen(courseId: course.id),
      ),
    );
  }
}
```

### 2. User Progress Tracking

```dart
class ProgressService {
  final ApiService _apiService = ApiService();

  Future<void> updateLessonProgress({
    required int courseId,
    required int lessonId,
    required double progressPercentage,
    int? timeSpentMinutes,
  }) async {
    try {
      await _apiService.updateLessonProgress(
        courseId,
        lessonId,
        progressPercentage: progressPercentage,
        timeSpentMinutes: timeSpentMinutes,
        isCompleted: progressPercentage >= 100,
      );
      
      print('Progress updated successfully');
    } catch (e) {
      print('Error updating progress: $e');
    }
  }

  Future<Map<String, dynamic>?> getCourseProgress(int courseId) async {
    try {
      return await _apiService.getUserProgress(courseId);
    } catch (e) {
      print('Error fetching progress: $e');
      return null;
    }
  }
}
```

### 3. Video Player with Progress Tracking

```dart
class VideoPlayerScreen extends StatefulWidget {
  final Lesson lesson;
  final int courseId;

  VideoPlayerScreen({required this.lesson, required this.courseId});

  @override
  _VideoPlayerScreenState createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  late VideoPlayerController _controller;
  final ProgressService _progressService = ProgressService();
  Timer? _progressTimer;
  int _watchTimeMinutes = 0;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
    _startProgressTracking();
  }

  void _initializeVideo() {
    _controller = VideoPlayerController.network(widget.lesson.videoUrl!)
      ..initialize().then((_) {
        setState(() {});
      });
  }

  void _startProgressTracking() {
    _progressTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      if (_controller.value.isPlaying) {
        _watchTimeMinutes++;
        _updateProgress();
      }
    });
  }

  void _updateProgress() {
    final position = _controller.value.position.inSeconds;
    final duration = _controller.value.duration.inSeconds;
    final progressPercentage = duration > 0 ? (position / duration) * 100 : 0;

    _progressService.updateLessonProgress(
      courseId: widget.courseId,
      lessonId: widget.lesson.id,
      progressPercentage: progressPercentage,
      timeSpentMinutes: _watchTimeMinutes,
    );
  }

  @override
  void dispose() {
    _progressTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.lesson.title)),
      body: Column(
        children: [
          if (_controller.value.isInitialized)
            AspectRatio(
              aspectRatio: _controller.value.aspectRatio,
              child: VideoPlayer(_controller),
            ),
          VideoProgressIndicator(_controller, allowScrubbing: true),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: Icon(_controller.value.isPlaying ? Icons.pause : Icons.play_arrow),
                onPressed: () {
                  setState(() {
                    _controller.value.isPlaying
                        ? _controller.pause()
                        : _controller.play();
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
```

## Testing and Debugging

### 1. API Testing Script

Create a test script to verify all endpoints:
```bash
# Test script (save as test_api.sh)
#!/bin/bash

BASE_URL="http://localhost:3001"

echo "Testing API endpoints..."

# Health check
echo "1. Health check:"
curl -X GET "$BASE_URL/health"
echo -e "\n"

# Get categories
echo "2. Get categories:"
curl -X GET "$BASE_URL/api/courses/categories"
echo -e "\n"

# Get courses
echo "3. Get courses:"
curl -X GET "$BASE_URL/api/courses?page=1&limit=5"
echo -e "\n"

# Get featured courses
echo "4. Get featured courses:"
curl -X GET "$BASE_URL/api/courses/featured"
echo -e "\n"

echo "API testing completed!"
```

### 2. Flutter Network Debugging

Add network logging to your Flutter app:
```dart
class DebugHttpClient extends HttpClient {
  @override
  Future<dynamic> get(String url, {Map<String, String>? queryParams}) async {
    print('🌐 GET Request: $url');
    if (queryParams != null) print('📋 Query Params: $queryParams');
    
    try {
      final result = await super.get(url, queryParams: queryParams);
      print('✅ GET Success: ${result.toString().substring(0, 100)}...');
      return result;
    } catch (e) {
      print('❌ GET Error: $e');
      rethrow;
    }
  }

  @override
  Future<dynamic> post(String url, {dynamic body}) async {
    print('🌐 POST Request: $url');
    if (body != null) print('📤 Request Body: $body');
    
    try {
      final result = await super.post(url, body: body);
      print('✅ POST Success: ${result.toString().substring(0, 100)}...');
      return result;
    } catch (e) {
      print('❌ POST Error: $e');
      rethrow;
    }
  }
}
```

### 3. Common Test Scenarios

```dart
class ApiTestService {
  final ApiService _apiService = ApiService();

  Future<void> runAllTests() async {
    print('🧪 Starting API integration tests...');

    await _testCourseEndpoints();
    await _testCategoryEndpoints();
    await _testUserEndpoints();
    await _testPaymentEndpoints();

    print('✅ All tests completed!');
  }

  Future<void> _testCourseEndpoints() async {
    try {
      print('📚 Testing course endpoints...');
      
      // Test get all courses
      final courses = await _apiService.getCourses(limit: 5);
      assert(courses.isNotEmpty, 'Courses list should not be empty');
      print('✅ Get courses: ${courses.length} courses found');

      // Test get course details
      final courseDetails = await _apiService.getCourseById(courses.first.id);
      assert(courseDetails.id == courses.first.id, 'Course ID should match');
      print('✅ Get course details: ${courseDetails.title}');

      // Test featured courses
      final featured = await _apiService.getFeaturedCourses();
      print('✅ Get featured courses: ${featured.length} featured courses');

    } catch (e) {
      print('❌ Course endpoints test failed: $e');
    }
  }

  Future<void> _testCategoryEndpoints() async {
    try {
      print('🏷️ Testing category endpoints...');
      
      final categories = await _apiService.getCategories();
      assert(categories.isNotEmpty, 'Categories list should not be empty');
      print('✅ Get categories: ${categories.length} categories found');

    } catch (e) {
      print('❌ Category endpoints test failed: $e');
    }
  }

  Future<void> _testUserEndpoints() async {
    try {
      print('👤 Testing user endpoints...');
      
      // Note: These require authentication
      // Test only if user is logged in
      if (FirebaseAuth.instance.currentUser != null) {
        final enrollments = await _apiService.getUserEnrollments();
        print('✅ Get enrollments: ${enrollments.length} enrollments found');
      } else {
        print('⚠️ Skipping user endpoints (not authenticated)');
      }

    } catch (e) {
      print('❌ User endpoints test failed: $e');
    }
  }

  Future<void> _testPaymentEndpoints() async {
    try {
      print('💳 Testing payment endpoints...');
      
      // Note: This creates a real payment order
      // Use only in test environment
      if (kDebugMode) {
        final orderData = await _apiService.createPaymentOrder(1);
        assert(orderData['orderId'] != null, 'Order ID should be present');
        print('✅ Create payment order: ${orderData['orderId']}');
      }

    } catch (e) {
      print('❌ Payment endpoints test failed: $e');
    }
  }
}
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Network Connection Issues

**Problem**: "No internet connection" or timeout errors
**Solutions**:
```dart
// Add network connectivity check
import 'package:connectivity_plus/connectivity_plus.dart';

Future<bool> checkConnectivity() async {
  final connectivityResult = await Connectivity().checkConnectivity();
  return connectivityResult != ConnectivityResult.none;
}

// Use in API calls
Future<List<Course>> getCourses() async {
  if (!await checkConnectivity()) {
    throw Exception('No internet connection');
  }
  // ... rest of the method
}
```

#### 2. Android Network Security

**Problem**: HTTP requests blocked on Android
**Solution**: Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<application
    android:usesCleartextTraffic="true"
    ... >
```

#### 3. iOS App Transport Security

**Problem**: HTTP requests blocked on iOS
**Solution**: Add to `ios/Runner/Info.plist`:
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

#### 4. Firebase Authentication Issues

**Problem**: Token verification fails
**Solutions**:
```dart
// Check token validity
Future<bool> isTokenValid() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return false;
    
    final token = await user.getIdToken(true); // Force refresh
    return token != null;
  } catch (e) {
    return false;
  }
}

// Refresh token if needed
Future<void> refreshTokenIfNeeded() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    await user?.getIdToken(true);
  } catch (e) {
    print('Token refresh failed: $e');
  }
}
```

#### 5. CORS Issues

**Problem**: CORS errors in web development
**Solution**: Backend already configured for CORS, but ensure frontend URL is allowed:
```javascript
// In backend server.js
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));
```

#### 6. Data Model Mismatches

**Problem**: JSON parsing errors
**Solution**: Add robust error handling in model classes:
```dart
class Course {
  // ... other fields

  factory Course.fromJson(Map<String, dynamic> json) {
    try {
      return Course(
        id: json['id']?.toString() ?? '',
        title: json['title']?.toString() ?? '',
        price: (json['price'] is String) 
            ? double.tryParse(json['price']) ?? 0.0
            : (json['price']?.toDouble() ?? 0.0),
        // ... other fields with null safety
      );
    } catch (e) {
      print('Error parsing Course JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }
}
```

### Debug Checklist

Before reporting issues, check:

1. ✅ Backend server is running on correct port (3001)
2. ✅ Database is connected and seeded with data
3. ✅ Flutter app has internet permissions
4. ✅ API base URL is correct for your platform (localhost vs ********)
5. ✅ Firebase authentication is properly configured
6. ✅ Network security settings allow HTTP requests
7. ✅ All required dependencies are installed
8. ✅ Environment variables are properly set

### Performance Optimization

1. **Implement Caching**:
```dart
class CachedApiService {
  final Map<String, dynamic> _cache = {};
  final Duration _cacheTimeout = Duration(minutes: 5);

  Future<List<Course>> getCourses() async {
    final cacheKey = 'courses';
    final cached = _cache[cacheKey];
    
    if (cached != null && 
        DateTime.now().difference(cached['timestamp']) < _cacheTimeout) {
      return cached['data'];
    }

    final courses = await _apiService.getCourses();
    _cache[cacheKey] = {
      'data': courses,
      'timestamp': DateTime.now(),
    };
    
    return courses;
  }
}
```

2. **Implement Pagination**:
```dart
class PaginatedCourseList extends StatefulWidget {
  @override
  _PaginatedCourseListState createState() => _PaginatedCourseListState();
}

class _PaginatedCourseListState extends State<PaginatedCourseList> {
  final ScrollController _scrollController = ScrollController();
  List<Course> courses = [];
  int currentPage = 1;
  bool isLoading = false;
  bool hasMore = true;

  @override
  void initState() {
    super.initState();
    _loadCourses();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreCourses();
    }
  }

  Future<void> _loadCourses() async {
    if (isLoading) return;
    
    setState(() => isLoading = true);
    
    try {
      final newCourses = await ApiService().getCourses(
        page: currentPage,
        limit: 10,
      );
      
      setState(() {
        if (currentPage == 1) {
          courses = newCourses;
        } else {
          courses.addAll(newCourses);
        }
        hasMore = newCourses.length == 10;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
    }
  }

  Future<void> _loadMoreCourses() async {
    if (!hasMore || isLoading) return;
    
    currentPage++;
    await _loadCourses();
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      itemCount: courses.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == courses.length) {
          return Center(child: CircularProgressIndicator());
        }
        return CourseCard(course: courses[index]);
      },
    );
  }
}
```

## Error Handling Best Practices

### 1. Standardized Error Response Format

All API endpoints return errors in this format:
```json
{
  "success": false,
  "message": "Error description",
  "code": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  }
}
```

### 2. Flutter Error Handling Implementation

```dart
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? code;
  final Map<String, dynamic>? details;

  ApiException(this.message, {this.statusCode, this.code, this.details});

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

class ErrorHandler {
  static String getErrorMessage(dynamic error) {
    if (error is ApiException) {
      return error.message;
    } else if (error is SocketException) {
      return 'No internet connection. Please check your network.';
    } else if (error is TimeoutException) {
      return 'Request timeout. Please try again.';
    } else if (error is FormatException) {
      return 'Invalid response format.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  static void showErrorSnackBar(BuildContext context, dynamic error) {
    final message = getErrorMessage(error);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}
```

### 3. Retry Logic Implementation

```dart
class RetryableApiService {
  final ApiService _apiService = ApiService();
  final int maxRetries = 3;
  final Duration retryDelay = Duration(seconds: 2);

  Future<T> executeWithRetry<T>(Future<T> Function() apiCall) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        return await apiCall();
      } catch (e) {
        attempts++;

        if (attempts >= maxRetries) {
          rethrow;
        }

        // Only retry on network errors, not on client errors (4xx)
        if (e is ApiException && e.statusCode != null && e.statusCode! < 500) {
          rethrow;
        }

        print('API call failed (attempt $attempts/$maxRetries): $e');
        await Future.delayed(retryDelay * attempts);
      }
    }

    throw Exception('Max retries exceeded');
  }
}
```

## Advanced Features Implementation

### 1. Offline Support with Local Storage

```dart
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class LocalDatabase {
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'quipgen.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE courses(
            id INTEGER PRIMARY KEY,
            title TEXT,
            description TEXT,
            imageUrl TEXT,
            instructor TEXT,
            price REAL,
            rating REAL,
            totalLessons INTEGER,
            duration INTEGER,
            category TEXT,
            level TEXT,
            isFeatured INTEGER,
            isPopular INTEGER,
            createdAt TEXT,
            updatedAt TEXT,
            syncedAt TEXT
          )
        ''');

        await db.execute('''
          CREATE TABLE user_progress(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            courseId INTEGER,
            lessonId INTEGER,
            progressPercentage REAL,
            timeSpentMinutes INTEGER,
            isCompleted INTEGER,
            lastAccessedAt TEXT,
            syncedAt TEXT
          )
        ''');
      },
    );
  }

  Future<void> cacheCourses(List<Course> courses) async {
    final db = await database;
    final batch = db.batch();

    for (final course in courses) {
      batch.insert(
        'courses',
        course.toJson()..['syncedAt'] = DateTime.now().toIso8601String(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit();
  }

  Future<List<Course>> getCachedCourses() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('courses');

    return List.generate(maps.length, (i) => Course.fromJson(maps[i]));
  }
}

class OfflineFirstApiService {
  final ApiService _apiService = ApiService();
  final LocalDatabase _localDb = LocalDatabase();

  Future<List<Course>> getCourses() async {
    try {
      // Try to fetch from API first
      final courses = await _apiService.getCourses();

      // Cache the results
      await _localDb.cacheCourses(courses);

      return courses;
    } catch (e) {
      print('API call failed, using cached data: $e');

      // Fallback to cached data
      return await _localDb.getCachedCourses();
    }
  }
}
```

### 2. Real-time Updates with WebSocket

```dart
import 'package:web_socket_channel/web_socket_channel.dart';

class RealtimeService {
  WebSocketChannel? _channel;
  final String wsUrl = 'ws://localhost:3001/ws';

  void connect() {
    try {
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));

      _channel!.stream.listen(
        (data) {
          final message = json.decode(data);
          _handleRealtimeMessage(message);
        },
        onError: (error) {
          print('WebSocket error: $error');
          _reconnect();
        },
        onDone: () {
          print('WebSocket connection closed');
          _reconnect();
        },
      );
    } catch (e) {
      print('Failed to connect to WebSocket: $e');
    }
  }

  void _handleRealtimeMessage(Map<String, dynamic> message) {
    switch (message['type']) {
      case 'course_updated':
        // Refresh course data
        break;
      case 'new_enrollment':
        // Update enrollment count
        break;
      case 'progress_updated':
        // Update user progress
        break;
    }
  }

  void _reconnect() {
    Future.delayed(Duration(seconds: 5), () {
      connect();
    });
  }

  void disconnect() {
    _channel?.sink.close();
  }
}
```

### 3. Background Sync Service

```dart
import 'package:workmanager/workmanager.dart';

class BackgroundSyncService {
  static const String syncTaskName = 'syncUserProgress';

  static void initialize() {
    Workmanager().initialize(callbackDispatcher);
  }

  static void scheduleSync() {
    Workmanager().registerPeriodicTask(
      syncTaskName,
      syncTaskName,
      frequency: Duration(hours: 1),
      constraints: Constraints(
        networkType: NetworkType.connected,
      ),
    );
  }

  static Future<void> syncPendingProgress() async {
    try {
      final localDb = LocalDatabase();
      final pendingProgress = await localDb.getPendingProgressUpdates();

      for (final progress in pendingProgress) {
        try {
          await ApiService().updateLessonProgress(
            progress.courseId,
            progress.lessonId,
            progressPercentage: progress.progressPercentage,
            timeSpentMinutes: progress.timeSpentMinutes,
            isCompleted: progress.isCompleted,
          );

          await localDb.markProgressAsSynced(progress.id);
        } catch (e) {
          print('Failed to sync progress ${progress.id}: $e');
        }
      }
    } catch (e) {
      print('Background sync failed: $e');
    }
  }
}

void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case BackgroundSyncService.syncTaskName:
        await BackgroundSyncService.syncPendingProgress();
        break;
    }
    return Future.value(true);
  });
}
```

## Security Best Practices

### 1. Secure Token Storage

```dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureTokenStorage {
  static const _storage = FlutterSecureStorage();
  static const String _tokenKey = 'firebase_token';
  static const String _refreshTokenKey = 'firebase_refresh_token';

  static Future<void> storeToken(String token) async {
    await _storage.write(key: _tokenKey, value: token);
  }

  static Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }

  static Future<void> clearTokens() async {
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _refreshTokenKey);
  }

  static Future<bool> hasValidToken() async {
    final token = await getToken();
    if (token == null) return false;

    try {
      // Verify token with backend
      final response = await ApiService().verifyToken();
      return response != null;
    } catch (e) {
      return false;
    }
  }
}
```

### 2. API Request Signing

```dart
import 'package:crypto/crypto.dart';

class ApiSecurity {
  static String generateRequestSignature(String method, String path, String body, String timestamp) {
    final message = '$method$path$body$timestamp';
    final key = utf8.encode('your_secret_key');
    final bytes = utf8.encode(message);
    final hmacSha256 = Hmac(sha256, key);
    final digest = hmacSha256.convert(bytes);
    return digest.toString();
  }

  static Map<String, String> getSecureHeaders(String method, String path, String body) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final signature = generateRequestSignature(method, path, body, timestamp);

    return {
      'X-Timestamp': timestamp,
      'X-Signature': signature,
    };
  }
}
```

## Performance Monitoring

### 1. API Performance Tracking

```dart
class PerformanceMonitor {
  static final Map<String, List<int>> _apiTimes = {};

  static Future<T> trackApiCall<T>(String endpoint, Future<T> Function() apiCall) async {
    final stopwatch = Stopwatch()..start();

    try {
      final result = await apiCall();
      stopwatch.stop();

      _recordApiTime(endpoint, stopwatch.elapsedMilliseconds);

      return result;
    } catch (e) {
      stopwatch.stop();
      _recordApiTime('$endpoint-error', stopwatch.elapsedMilliseconds);
      rethrow;
    }
  }

  static void _recordApiTime(String endpoint, int milliseconds) {
    if (!_apiTimes.containsKey(endpoint)) {
      _apiTimes[endpoint] = [];
    }

    _apiTimes[endpoint]!.add(milliseconds);

    // Keep only last 100 measurements
    if (_apiTimes[endpoint]!.length > 100) {
      _apiTimes[endpoint]!.removeAt(0);
    }
  }

  static Map<String, double> getAverageApiTimes() {
    final averages = <String, double>{};

    _apiTimes.forEach((endpoint, times) {
      if (times.isNotEmpty) {
        averages[endpoint] = times.reduce((a, b) => a + b) / times.length;
      }
    });

    return averages;
  }

  static void logPerformanceReport() {
    final averages = getAverageApiTimes();
    print('📊 API Performance Report:');

    averages.forEach((endpoint, avgTime) {
      final status = avgTime < 1000 ? '✅' : avgTime < 3000 ? '⚠️' : '❌';
      print('$status $endpoint: ${avgTime.toStringAsFixed(0)}ms');
    });
  }
}
```

### 2. Memory Usage Optimization

```dart
class MemoryOptimizer {
  static void optimizeImageLoading() {
    // Configure image cache
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50MB
  }

  static void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  static void optimizeListViews() {
    // Use ListView.builder with proper itemExtent
    // Implement lazy loading for large lists
    // Use RepaintBoundary for complex list items
  }
}
```

## Production Deployment Checklist

### Backend Deployment

1. **Environment Configuration**:
```bash
# Production .env
NODE_ENV=production
PORT=3001
DATABASE_URL=***********************************/quipgen_prod
FIREBASE_ADMIN_SDK_PATH=/app/config/firebase-admin-sdk.json
RAZORPAY_KEY_ID=rzp_live_xxxxx
RAZORPAY_KEY_SECRET=live_secret_xxxxx
CORS_ORIGIN=https://your-app-domain.com
```

2. **Security Headers**:
```javascript
// Add to server.js
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
```

3. **Database Optimization**:
```sql
-- Add indexes for better performance
CREATE INDEX idx_courses_category_id ON courses(category_id);
CREATE INDEX idx_courses_is_featured ON courses(is_featured);
CREATE INDEX idx_courses_is_popular ON courses(is_popular);
CREATE INDEX idx_enrollments_user_id ON enrollments(user_id);
CREATE INDEX idx_user_progress_user_course ON user_progress(user_id, course_id);
```

### Flutter Production Build

1. **Build Configuration**:
```yaml
# pubspec.yaml
flutter:
  assets:
    - assets/images/
    - assets/icons/

# Build commands
flutter build apk --release
flutter build ios --release
flutter build web --release
```

2. **Production API Configuration**:
```dart
class ApiConfig {
  static String get apiBaseUrl {
    switch (environment) {
      case 'production':
        return 'https://api.your-domain.com/api';
      case 'staging':
        return 'https://staging-api.your-domain.com/api';
      default:
        return 'http://localhost:3001/api';
    }
  }
}
```

3. **Release Checklist**:
- [ ] Update API base URLs for production
- [ ] Configure proper Firebase project
- [ ] Set up Razorpay production keys
- [ ] Enable ProGuard/R8 for Android
- [ ] Configure app signing
- [ ] Test on physical devices
- [ ] Verify all network security configurations
- [ ] Test offline functionality
- [ ] Validate payment flow end-to-end
- [ ] Check app permissions
- [ ] Verify deep linking
- [ ] Test push notifications (if implemented)

This comprehensive guide covers all aspects of integrating the Flutter frontend with the Node.js backend. Follow the sections step by step to establish a complete, working integration with proper error handling, authentication, security, performance optimization, and production deployment considerations.
