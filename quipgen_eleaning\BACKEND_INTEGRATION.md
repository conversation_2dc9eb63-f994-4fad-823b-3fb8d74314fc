# Backend Integration Guide

This document outlines the complete backend integration implementation for the QuipGen E-Learning Flutter application.

## 🎯 Integration Overview

The Flutter app has been successfully integrated with the Node.js backend API. The integration includes:

- ✅ Complete API service layer with all endpoints
- ✅ Firebase authentication integration
- ✅ Razorpay payment processing
- ✅ Network connectivity handling
- ✅ Comprehensive error handling
- ✅ Offline support with fallback data
- ✅ Platform-specific network configuration

## 🔧 Configuration Changes

### 1. Dependencies Added

```yaml
# Payment integration
razorpay_flutter: ^1.3.6
# Network connectivity
connectivity_plus: ^6.0.5
# Local storage for offline support
sqflite: ^2.3.3+1
path: ^1.9.0
```

### 2. API Configuration

- **Base URL**: Updated to use correct port (3001)
- **Platform Support**: 
  - Android Emulator: `http://********:3001/api`
  - iOS Simulator: `http://localhost:3001/api`
  - Physical Device: `http://*************:3001/api` (update with your IP)

### 3. Network Security

**Android** (`android/app/src/main/AndroidManifest.xml`):
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<application android:usesCleartextTraffic="true">
```

**iOS** (`ios/Runner/Info.plist`):
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

## 🏗️ Architecture

### Service Layer Structure

```
lib/services/
├── api_config.dart          # API endpoints and configuration
├── http_client.dart         # HTTP client with auth and error handling
├── api_service.dart         # API service with all endpoints
├── data_service.dart        # Data service with offline fallback
├── payment_service.dart     # Razorpay payment integration
├── connectivity_service.dart # Network connectivity monitoring
├── error_handler.dart       # Centralized error handling
└── backend_test_service.dart # Integration testing
```

### Key Features

1. **Network-Aware Operations**: Automatically handles online/offline scenarios
2. **Error Handling**: Comprehensive error categorization and user-friendly messages
3. **Payment Integration**: Complete Razorpay workflow with backend verification
4. **Connectivity Monitoring**: Real-time network status tracking
5. **Fallback Support**: Graceful degradation to mock data when backend unavailable

## 🔌 API Endpoints Integrated

### Authentication
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `GET /api/auth/verify` - Verify Firebase token

### Courses
- `GET /api/courses` - Get courses with pagination and filters
- `GET /api/courses/:id` - Get course details
- `GET /api/courses/featured` - Get featured courses
- `GET /api/courses/popular` - Get popular courses
- `GET /api/courses/categories` - Get course categories

### User Management
- `GET /api/user/enrollments` - Get user enrollments
- `GET /api/user/progress/:courseId` - Get course progress
- `PUT /api/user/progress/:courseId/:lessonId` - Update lesson progress

### Lessons
- `GET /api/lessons/course/:courseId` - Get course lessons
- `GET /api/lessons/:id` - Get lesson details
- `POST /api/lessons/:id/access` - Mark lesson as accessed

### Payments
- `POST /api/payment/create-order` - Create Razorpay order
- `POST /api/payment/verify` - Verify payment
- `GET /api/payment/history` - Get payment history

## 💳 Payment Integration

### Razorpay Setup

1. **Initialize Payment Service**:
```dart
final paymentService = PaymentService();
paymentService.initialize();
```

2. **Purchase Course**:
```dart
await paymentService.purchaseCourse(
  course: course,
  userEmail: '<EMAIL>',
  userPhone: '+1234567890',
  onSuccess: (response) {
    // Handle successful payment
  },
  onError: (response) {
    // Handle payment failure
  },
);
```

### Payment Flow

1. User initiates course purchase
2. App creates payment order with backend
3. Razorpay checkout opens
4. User completes payment
5. App verifies payment with backend
6. User gets enrolled in course

## 🧪 Testing

### Backend Test Screen

Access the test screen at `/backend-test` route to:

- Check system status (connectivity, auth, payment service)
- Run comprehensive API endpoint tests
- Test specific course operations
- Monitor real-time connectivity status

### Running Tests

```dart
// Run all integration tests
final testService = BackendTestService();
await testService.runAllTests();

// Quick connectivity test
final isConnected = await testService.testBasicConnectivity();

// Test specific course
final success = await testService.testCourseOperations(1);
```

## 🔄 Error Handling

### Error Types

- **Network**: Connection issues, timeouts
- **Authentication**: Login failures, token issues
- **Validation**: Invalid input data
- **Server**: Backend errors (5xx)
- **Payment**: Payment processing failures

### Usage

```dart
try {
  final courses = await apiService.getCourses();
} catch (e) {
  final error = ErrorHandler.handleError(e);
  ErrorHandler.showErrorSnackBar(context, error);
}
```

## 🌐 Offline Support

### Network-Aware Operations

```dart
final operation = NetworkAwareOperation<List<Course>>(
  onlineOperation: () => apiService.getCourses(),
  offlineOperation: () async => cachedCourses,
  fallbackData: mockCourses,
  operationName: 'getCourses',
);

final courses = await operation.execute();
```

### Connectivity Monitoring

```dart
ConnectivityService().statusStream.listen((status) {
  switch (status) {
    case ConnectionStatus.online:
      // Handle online state
      break;
    case ConnectionStatus.offline:
      // Handle offline state
      break;
  }
});
```

## 🚀 Getting Started

### 1. Install Dependencies

```bash
cd quipgen_eleaning
flutter pub get
```

### 2. Configure Backend URL

Update `lib/services/api_config.dart` with your backend URL:

```dart
static String get physicalDeviceBaseUrl => 'http://YOUR_IP:3001/api';
```

### 3. Start Backend Server

```bash
cd backend
npm install
npm run dev
```

### 4. Run Flutter App

```bash
flutter run
```

### 5. Test Integration

Navigate to `/backend-test` in the app to run integration tests.

## 🔧 Troubleshooting

### Common Issues

1. **Connection Refused**: Check backend is running on port 3001
2. **CORS Errors**: Ensure backend allows Flutter app origin
3. **Auth Failures**: Verify Firebase configuration
4. **Payment Issues**: Check Razorpay key configuration

### Debug Mode

Enable debug logging by setting `kDebugMode` to true for detailed API logs.

## 📱 Platform Notes

### Android
- Uses `********` for emulator localhost access
- Requires `INTERNET` permission
- Needs `usesCleartextTraffic="true"` for HTTP

### iOS
- Uses `localhost` for simulator access
- Requires App Transport Security configuration
- Supports HTTP in development mode

## 🔐 Security

- Firebase JWT tokens for authentication
- Secure token storage using Flutter Secure Storage
- Payment verification through backend
- Network request signing (optional)

## 📊 Monitoring

The integration includes comprehensive monitoring:

- Real-time connectivity status
- API response times
- Error tracking and categorization
- Payment transaction logging
- User session management

---

**Note**: This integration is production-ready but ensure to update API URLs, payment keys, and security configurations for production deployment.
